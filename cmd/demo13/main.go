package main

import (
	"fmt"
	"math"
	"strings"
	"time"

	"github.com/hablullah/go-prayer"
)

/**
总结表格
马来/印尼语词汇	阿拉伯语词汇	中文含义	类型	时间描述
Imsak	Imsāk	戒食时刻	斋戒辅助时间	晨礼前约10-15分钟，提醒停止进食
Subuh	Fajr	晨礼	强制祷告	黎明破晓至日出前
Terbit	Shurūq	日出	天文时间点	太阳升起，晨礼时间结束
Dhuha	Duha	都哈拜	自愿祷告	日出后至晌礼前
Zuhur	Dhuhr	晌礼	强制祷告	正午太阳偏西后至晡礼前
Ashar	Asr		晡礼	强制祷告	晌礼后至日落前
Maghrib	Maghrib	昏礼	强制祷告	日落后至晚霞消失
Isya'	<PERSON>ha'	宵礼	强制祷告	晚霞消失后至午夜
*/

// Mathematical constants
const (
	DegreesToRadians = math.Pi / 180.0
	RadiansToDegrees = 180.0 / math.Pi
)

// Calculate Julian day from date
func getJulianDay(date time.Time) float64 {
	utc := date.UTC()
	year := utc.Year()
	month := int(utc.Month())
	day := utc.Day()

	if month <= 2 {
		year--
		month += 12
	}

	a := year / 100
	b := 2 - a + a/4

	jd := math.Floor(365.25*float64(year+4716)) +
		math.Floor(30.6001*float64(month+1)) +
		float64(day) + float64(b) - 1524.5

	return jd
}

// Calculate solar declination
func getSolarDeclination(jd float64) float64 {
	n := jd - 2451545.0
	L := math.Mod(280.460+0.9856474*n, 360.0)
	g := math.Mod(357.528+0.9856003*n, 360.0)
	lambda := L + 1.915*math.Sin(g*DegreesToRadians) + 0.020*math.Sin(2*g*DegreesToRadians)

	return math.Asin(math.Sin(23.439*DegreesToRadians)*math.Sin(lambda*DegreesToRadians)) * RadiansToDegrees
}

// Calculate equation of time
func getEquationOfTime(jd float64) float64 {
	n := jd - 2451545.0
	L := math.Mod(280.460+0.9856474*n, 360.0)
	g := math.Mod(357.528+0.9856003*n, 360.0)
	lambda := L + 1.915*math.Sin(g*DegreesToRadians) + 0.020*math.Sin(2*g*DegreesToRadians)

	alpha := math.Atan2(math.Cos(23.439*DegreesToRadians)*math.Sin(lambda*DegreesToRadians),
		math.Cos(lambda*DegreesToRadians)) * RadiansToDegrees

	return 4 * (L - alpha)
}

// Calculate hour angle for a given solar elevation
func getHourAngle(latitude, declination, angle float64) float64 {
	latRad := latitude * DegreesToRadians
	decRad := declination * DegreesToRadians
	angleRad := angle * DegreesToRadians

	numerator := math.Sin(angleRad) - math.Sin(latRad)*math.Sin(decRad)
	denominator := math.Cos(latRad) * math.Cos(decRad)

	if math.Abs(numerator/denominator) > 1 {
		return math.NaN() // No solution (polar regions)
	}

	return math.Acos(numerator/denominator) * RadiansToDegrees
}

// timeForAngle calculates time when sun is at specific angle (copied from demo6)
func timeForAngle(date time.Time, latitude, longitude, angle float64, afterTransit bool) float64 {
	jd := getJulianDay(date)
	n := jd - 2451545.0

	// Mean longitude of the sun
	L := math.Mod(280.460+0.9856474*n, 360.0)

	// Mean anomaly
	g := math.Mod(357.528+0.9856003*n, 360.0)

	// Ecliptic longitude
	lambda := math.Mod(L+1.915*math.Sin(g*DegreesToRadians)+0.020*math.Sin(2*g*DegreesToRadians), 360.0)

	// Declination
	declination := math.Asin(math.Sin(23.439*DegreesToRadians) * math.Sin(lambda*DegreesToRadians))

	// Equation of time
	eot := 4 * (L - 0.0057183 - math.Atan2(math.Tan(lambda*DegreesToRadians), math.Cos(23.439*DegreesToRadians)))

	// Hour angle calculation
	cosHourAngle := (math.Sin(angle*DegreesToRadians) -
		math.Sin(latitude*DegreesToRadians)*math.Sin(declination)) /
		(math.Cos(latitude*DegreesToRadians) * math.Cos(declination))

	if cosHourAngle < -1 || cosHourAngle > 1 {
		return math.NaN() // Sun doesn't reach this angle
	}

	hourAngle := math.Acos(cosHourAngle)
	transit := 12 - longitude/15 - eot/60

	if afterTransit {
		return transit + hourAngle*RadiansToDegrees/15
	}
	return transit - hourAngle*RadiansToDegrees/15
}

// Calculate Dhuha time using true 4.5° solar elevation angle
func calculateDhuhaTime(date time.Time, latitude, longitude float64) time.Time {
	// For Dhuha, we want the morning time when sun reaches 4.5° after sunrise
	// This should be afterTransit=true (after solar noon calculation, but actually before noon in time)
	// Let me try the opposite of what demo6 does
	dhuhaDecimal := timeForAngle(date, latitude, longitude, 4.5, true)

	if math.IsNaN(dhuhaDecimal) {
		// Fallback if calculation fails
		return time.Time{}
	}

	// Handle day boundary crossings
	for dhuhaDecimal < 0 {
		dhuhaDecimal += 24
	}
	for dhuhaDecimal >= 24 {
		dhuhaDecimal -= 24
	}

	// Convert decimal hours to time components
	hours := int(dhuhaDecimal)
	minutes := int((dhuhaDecimal - float64(hours)) * 60)
	seconds := int(((dhuhaDecimal-float64(hours))*60 - float64(minutes)) * 60)

	// Create time in local timezone
	return time.Date(date.Year(), date.Month(), date.Day(), hours, minutes, seconds, 0, date.Location())
}

func main() {
	// 23.116672,113.375473
	asiaJakarta, _ := time.LoadLocation("Asia/Shanghai")
	jakartaSchedules, _ := prayer.Calculate(prayer.Config{
		Latitude:           23.116672,
		Longitude:          113.375473,
		Timezone:           asiaJakarta,
		TwilightConvention: prayer.Diyanet(),
		AsrConvention:      prayer.Hanafi,
		PreciseToSeconds:   true,
		Corrections: prayer.ScheduleCorrections{
			Fajr:    time.Duration(0) * time.Second,
			Sunrise: time.Duration(0) * time.Second,
			Zuhr:    time.Duration(2) * time.Minute,
			Asr:     time.Duration(0) * time.Second,
			Maghrib: time.Duration(0) * time.Second,
			Isha:    time.Duration(0) * time.Second,
		},
	}, 2025)

	for _, schedule := range jakartaSchedules {
		date := schedule.Date
		if strings.Contains(date, "2025-08-") {
			// Parse the date string to get a proper time.Time object
			scheduleDate, _ := time.ParseInLocation("2006-01-02", date, asiaJakarta)

			// Calculate Dhuha using true 4.5° solar elevation angle
			dhuhaTime := calculateDhuhaTime(scheduleDate, 23.116672, 113.375473)

			fmt.Printf("Date: %s\n", date)
			fmt.Printf("Imsak: %v\n", schedule.Fajr.Add(-time.Minute*10).Format("15:04:05"))
			fmt.Printf("Fajr: %s\n", schedule.Fajr.Format("15:04:05"))
			fmt.Printf("Sunrise: %s\n", schedule.Sunrise.Format("15:04:05"))
			fmt.Printf("Dhuha: %s\n", dhuhaTime.Format("15:04:05"))
			fmt.Printf("Zuhr: %s\n", schedule.Zuhr.Format("15:04:05"))
			fmt.Printf("Asr: %s\n", schedule.Asr.Format("15:04:05"))
			fmt.Printf("Maghrib: %s\n", schedule.Maghrib.Format("15:04:05"))
			fmt.Printf("Isha: %s\n", schedule.Isha.Format("15:04:05"))
			fmt.Println("--------------------------------")
		}
	}
}
