package main

import (
	"fmt"
	"math"
	"strings"
	"time"

	"github.com/hablullah/go-prayer"
)

/**
总结表格
马来/印尼语词汇	阿拉伯语词汇	中文含义	类型	时间描述
Imsak	Imsāk	戒食时刻	斋戒辅助时间	晨礼前约10-15分钟，提醒停止进食
Subuh	Fajr	晨礼	强制祷告	黎明破晓至日出前
Terbit	Shurūq	日出	天文时间点	太阳升起，晨礼时间结束
Dhuha	Duha	都哈拜	自愿祷告	日出后至晌礼前
Zuhur	Dhuhr	晌礼	强制祷告	正午太阳偏西后至晡礼前
Ashar	Asr		晡礼	强制祷告	晌礼后至日落前
Maghrib	Maghrib	昏礼	强制祷告	日落后至晚霞消失
Isya'	Isha'	宵礼	强制祷告	晚霞消失后至午夜
*/

func main() {
	var (
		latitude  = 23.118889
		longitude = 113.37
	)
	// 23.116672,113.375473
	timezoneLoc, _ := time.LoadLocation("Asia/Shanghai")
	jakartaSchedules, _ := prayer.Calculate(prayer.Config{
		Latitude:           latitude,
		Longitude:          longitude,
		Elevation:          0, // TODO: 海拔？
		Timezone:           timezoneLoc,
		TwilightConvention: prayer.MWL(),
		AsrConvention:      prayer.Shafii,
		PreciseToSeconds:   true,
		Corrections: prayer.ScheduleCorrections{
			Fajr:    time.Duration(0) * time.Second,
			Sunrise: time.Duration(0) * time.Second,
			Zuhr:    time.Duration(2) * time.Minute,
			Asr:     time.Duration(0) * time.Second,
			Maghrib: time.Duration(0) * time.Second,
			Isha:    time.Duration(0) * time.Second,
		},
	}, 2025)

	for _, schedule := range jakartaSchedules {
		dateStr := schedule.Date
		date, _ := time.ParseInLocation("2006-01-02", dateStr, timezoneLoc)

		dhuha := calculateDhuhaTime(date, latitude, longitude, schedule.Sunrise)
		if strings.Contains(dateStr, "2025-08-") {
			// fmt.Printf("%s = %v\n", date, schedule)

			// fmt.Printf("IsNormal: %v\n", schedule.IsNormal)
			fmt.Printf("Date: %s\n", dateStr)
			fmt.Printf("Imsak: %v\n", formatTime(schedule.Fajr.Add(-time.Minute*10)))
			fmt.Printf("Subuh: %s\n", formatTime(schedule.Fajr))
			// fmt.Printf("Sunrise: %s\n", schedule.Sunrise.Format("15:04:05"))
			fmt.Printf("Sunrise: %s\n", formatTime(schedule.Sunrise))
			fmt.Printf("Dhuha: %s\n", formatTime(dhuha))
			fmt.Printf("Zuhr: %s\n", formatTime(schedule.Zuhr))
			fmt.Printf("Asr: %s\n", formatTime(schedule.Asr))
			fmt.Printf("Maghrib: %s\n", formatTime(schedule.Maghrib))
			fmt.Printf("Isha: %s\n", formatTime(schedule.Isha))
			fmt.Println("--------------------------------")
		}
	}
}

// THIS IS THE FUNCTION TO REPLACE IN YOUR CODE
// Calculate Dhuha time using true 4.5° solar elevation angle
func calculateDhuhaTime(date time.Time, latitude, longitude float64, sunrise time.Time) time.Time {
	// Get astronomical parameters
	jd := getJulianDayNumber(date)
	declination := getSolarDeclinationAngle(jd)

	// Calculate hour angles for sunrise (-0.833°) and Dhuha (4.5°)
	sunriseHourAngle := getHourAngleForElevation(latitude, declination, -0.833)
	dhuhaHourAngle := getHourAngleForElevation(latitude, declination, 4.5)

	if math.IsNaN(sunriseHourAngle) || math.IsNaN(dhuhaHourAngle) {
		// Fallback to approximate 24 minutes if calculation fails
		return sunrise.Add(24 * time.Minute)
	}

	// Calculate time difference in hours
	// Both are on the morning side, so the difference is the offset
	timeDiffHours := (sunriseHourAngle - dhuhaHourAngle) / 15.0

	// Add this offset to sunrise time
	return sunrise.Add(time.Duration(timeDiffHours*3600) * time.Second)
}

const (
	Deg2Rad = math.Pi / 180.0
	Rad2Deg = 180.0 / math.Pi
)

// Calculate Julian day number
func getJulianDayNumber(date time.Time) float64 {
	utc := date.UTC()
	year := utc.Year()
	month := int(utc.Month())
	day := utc.Day()

	if month <= 2 {
		year--
		month += 12
	}

	a := year / 100
	b := 2 - a + a/4

	jd := math.Floor(365.25*float64(year+4716)) +
		math.Floor(30.6001*float64(month+1)) +
		float64(day) + float64(b) - 1524.5

	return jd
}

// Calculate solar declination
func getSolarDeclinationAngle(jd float64) float64 {
	n := jd - 2451545.0
	L := math.Mod(280.460+0.9856474*n, 360.0)
	g := math.Mod(357.528+0.9856003*n, 360.0)
	lambda := L + 1.915*math.Sin(g*Deg2Rad) + 0.020*math.Sin(2*g*Deg2Rad)

	return math.Asin(math.Sin(23.439*Deg2Rad)*math.Sin(lambda*Deg2Rad)) * Rad2Deg
}

// Calculate hour angle for given solar elevation
func getHourAngleForElevation(latitude, declination, elevation float64) float64 {
	latRad := latitude * Deg2Rad
	decRad := declination * Deg2Rad
	elevRad := elevation * Deg2Rad

	numerator := math.Sin(elevRad) - math.Sin(latRad)*math.Sin(decRad)
	denominator := math.Cos(latRad) * math.Cos(decRad)

	if math.Abs(numerator/denominator) > 1 {
		return math.NaN() // No solution
	}

	return math.Acos(numerator/denominator) * Rad2Deg
}

// 时间转换的方法，如果秒是30以上，就进位到下一分钟，否则向下取整到当前分钟。最后输出是HH:mm
func formatTime(t time.Time) string {
	if t.Second() > 30 {
		return t.Add(time.Minute).Truncate(time.Minute).Format("15:04")
	}
	return t.Format("15:04")
}
