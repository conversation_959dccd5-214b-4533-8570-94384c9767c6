package main

import (
	"fmt"
	"strings"
	"time"

	"github.com/hablullah/go-prayer"
)

/**
总结表格
马来/印尼语词汇	阿拉伯语词汇	中文含义	类型	时间描述
Imsak	Imsāk	戒食时刻	斋戒辅助时间	晨礼前约10-15分钟，提醒停止进食
Subuh	Fajr	晨礼	强制祷告	黎明破晓至日出前
Terbit	Shurūq	日出	天文时间点	太阳升起，晨礼时间结束
Dhuha	Duha	都哈拜	自愿祷告	日出后至晌礼前
Zuhur	Dhuhr	晌礼	强制祷告	正午太阳偏西后至晡礼前
Ashar	Asr		晡礼	强制祷告	晌礼后至日落前
Maghrib	Maghrib	昏礼	强制祷告	日落后至晚霞消失
Isya'	Isha'	宵礼	强制祷告	晚霞消失后至午夜
*/

func main() {
	// 23.116672,113.375473
	asiaJakarta, _ := time.LoadLocation("Asia/Shanghai")
	jakartaSchedules, _ := prayer.Calculate(prayer.Config{
		Latitude:           23.116672,
		Longitude:          113.375473,
		Timezone:           asiaJakarta,
		TwilightConvention: prayer.Diyanet(),
		AsrConvention:      prayer.Shafii,
		PreciseToSeconds:   true,
		Corrections: prayer.ScheduleCorrections{
			Fajr:    time.Duration(0) * time.Second,
			Sunrise: time.Duration(0) * time.Second,
			Zuhr:    time.Duration(2) * time.Minute,
			Asr:     time.Duration(0) * time.Second,
			Maghrib: time.Duration(0) * time.Second,
			Isha:    time.Duration(0) * time.Second,
		},
	}, 2025)

	for _, schedule := range jakartaSchedules {
		date := schedule.Date
		if strings.Contains(date, "2025-08-") {
			// fmt.Printf("%s = %v\n", date, schedule)

			// fmt.Printf("IsNormal: %v\n", schedule.IsNormal)
			fmt.Printf("Date: %s\n", date)
			// fmt.Printf("Imsak: %v\n", schedule.Fajr.Add(-time.Minute*10).Format("15:04:05"))
			// fmt.Printf("Fajr: %s\n", schedule.Fajr.Format("15:04:05"))
			// fmt.Printf("Sunrise: %s\n", schedule.Sunrise.Format("15:04:05"))
			fmt.Printf("Sunrise: %s\n", schedule.Sunrise.Format("15:04:05"))
			fmt.Printf("Dhuha: %s\n", schedule.Sunrise.Add(24*time.Minute).Format("15:04:05"))
			// fmt.Printf("Zuhr: %s\n", schedule.Zuhr.Format("15:04:05"))
			// fmt.Printf("Asr: %s\n", schedule.Asr.Format("15:04:05"))
			// fmt.Printf("Maghrib: %s\n", schedule.Maghrib.Format("15:04:05"))
			// fmt.Printf("Isha: %s\n", schedule.Isha.Format("15:04:05"))
			fmt.Println("--------------------------------")
		}
	}
}
